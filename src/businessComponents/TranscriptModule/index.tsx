import { useImperativeHandle, forwardRef, useState, useEffect, useRef } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'

import './index.less'
import {
  message,
  Card,
  Space,
  Button,
  Form,
  Input,
  Progress,
  List,
  Divider,
  Typography,
} from 'antd'

import { ReloadOutlined, PlayCircleOutlined, VideoCameraOutlined } from '@ant-design/icons'
import { intelligentInterview } from '@/api/intelligentInterview'

export interface TranscriptModuleRef {
  getMentionsData: () => any
}

interface TranscriptModuleProps {
  globalLoading?: boolean
  setGlobalLoading?: (loading: boolean) => void
  videoInterviewData: any
  currentEchoData: any
  onCallParent: (type: string, data?: any) => void
}
const { Text, Title } = Typography

const TranscriptModule = forwardRef<TranscriptModuleRef, TranscriptModuleProps>(
  ({ videoInterviewData, globalLoading, setGlobalLoading, currentEchoData, onCallParent }, ref) => {
    // 右侧转录示例数据（静态，严格还原示例文案）
    const messages = [
      {
        id: 1,
        name: '小云',
        time: '00:00',
        color: 'green',
        text: '求助求助！今天的会议内容太多了，我根本记不住。',
      },
      {
        id: 2,
        name: '小超',
        time: '00:05',
        color: 'orange',
        text: '是的啊，讲话的人太多了，我甚至来不及做笔记，哎，好难。',
      },
      {
        id: 3,
        name: '听见小助手',
        time: '00:11',
        color: 'blue',
        text: '嗯，小伙伴们别担心，我已经帮你们刚才的会议内容整理记录下来啦，你看看会议的总结和关键段落呢。',
      },
      {
        id: 4,
        name: '小云',
        time: '00:21',
        color: 'green',
        text: '哇哦！你是怎么做到的？',
      },
    ]
    const [meetingData, setMeetingData] = useState<any>({})
    const [finalTranscript, setFinalTranscript] = useState('二分三分威风威风') // 转录结果数据
    const [recordFiles, setRecordFiles] = useState<any[]>([]) // 录制文件
    const [retryCountdown, setRetryCountdown] = useState(0) // 倒计时
    const [isRetrying, setIsRetrying] = useState(false) // 是否重试
    const [videoUrl, setVideoUrl] = useState('') // 回放地址
    const [isTranscribing, setIsTranscribing] = useState(false) // 是否转录
    const [transcriptProgress, setTranscriptProgress] = useState(0) // 转录进度
    const [loadingRecordFiles, setLoadingRecordFiles] = useState(false) // 加载录制文件
    const [transcriptSegments, setTranscriptSegments] = useState<any[]>([]) // 转录实时片段
    const [form] = Form.useForm()
    const isClick = useRef(false); //下一步是否禁用
    // 获取录制文件
    const handleGetRecordFiles = async (isRetryAttempt = false) => {
      if (!meetingData?.webinarId) {
        message.error('会议ID不存在')
        return
      }

      setLoadingRecordFiles(true)
      try {
        const files = await intelligentInterview.getRecordFiles(
          meetingData.webinarId
        )

        setRecordFiles(files)

        if (files.length === 0) {
          if (!isRetryAttempt) {
            // 第一次没找到文件，启动倒计时重试
            message.info('暂无录制文件，将在60秒后自动重试获取...')
            startRetryCountdown()
          } else {
            // 重试后仍然没找到
            message.warning(
              '重试后仍未找到录制文件，请确认面试已结束并开启了录制功能'
            )
          }
        } else {
          message.success(`找到 ${files.length} 个录制文件`)
          // 如果是重试成功，清除倒计时
          if (isRetryAttempt) {
            setRetryCountdown(0)
            setIsRetrying(false)
          }
        }
      } catch (error) {
        console.error('获取录制文件失败:', error)
        message.error('获取录制文件失败')
      } finally {
        setLoadingRecordFiles(false)
      }
    }
    // 启动重试倒计时
    const startRetryCountdown = () => {
      setIsRetrying(true)
      setRetryCountdown(60)

      const timer = setInterval(() => {
        setRetryCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            setIsRetrying(false)
            // 倒计时结束，自动重试
            handleGetRecordFiles(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    const handleAutoStart = () => {
      if (!videoUrl.trim()) {
        message.error('请输入录像地址')
        return
      }
      // 先注掉单元输入输出评审
      onCallParent('输入', videoUrl)
      // handleStartTranscript()
    }

    // 处理录像转录（使用说话人识别）- 与老项目保持一致
    const handleStartTranscript = async () => {
      if (!videoUrl.trim()) {
        message.error('请输入录像地址')
        return
      }
      setGlobalLoading?.(true)
      setTranscriptProgress(0)
      setTranscriptSegments([])
      setFinalTranscript('')

      try {
        await intelligentInterview.generateSpeakerTranscript(videoUrl, {
          onProgress: (progress) => {
            setTranscriptProgress(progress)
          },
          onSegment: (segment) => {
            setTranscriptSegments((prev) => [...prev, segment])
          },
          onComplete: async (transcript) => {
            setFinalTranscript(transcript)
            // onTranscriptUpdate(transcript)

            // // 保存转录结果到数据库 - 与老项目字段保持一致
            // await intelligentInterview.updateInterviewData(sessionId, {
            //   video_url: videoUrl,
            //   transcript_md: transcript
            // })
            isClick.current = true; //下一步可以点击
            message.success('转录完成！')
            setIsTranscribing(true)
            setGlobalLoading?.(false)
            // 先注掉单元输入输出评审
            onCallParent('输出', transcript)
          },
          onError: (error) => {
            message.error(`转录失败: ${error}`)
            // setIsTranscribing(false)
            setGlobalLoading?.(false)
          },
        })
      } catch (error) {
        console.error('转录失败:', error)
        message.error('转录失败，请稍后重试')
        // setIsTranscribing(false)
        setGlobalLoading?.(false)
      }
    }
    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getMentionsData: () => {
        return {
          finalTranscript: finalTranscript,
          videoUrl: videoUrl,
          isClick: isClick.current, //下一步是否可点击
        }
      },
      handleStartTranscript: handleStartTranscript,
    }))

    useEffect(() => {
      if (videoInterviewData && Object.keys(videoInterviewData).length > 0) {
        console.log(videoInterviewData, 'videoInterviewData')
        setMeetingData(videoInterviewData)
      }
      if(currentEchoData && Object.keys(currentEchoData).length > 0) {
        setVideoUrl(currentEchoData.videoUrl)
        setFinalTranscript(currentEchoData.finalTranscript)
        setIsTranscribing(true)
        isClick.current = currentEchoData?.isClick; //下一步可以点击
      }
    }, [])

    return (
      <div className="transcript-module">
        {isTranscribing ? (
          <div className="interview-container">
            <div className="left-section">
              <div className="section-title">面试回放</div>
              <div className="video-preview-card">
                {/* <div className="video-bg" /> */}
                {videoUrl ? (
                  <div className="iframe-container">
                    <iframe
                      src={String(videoUrl)}
                      width="100%"
                      height="600"
                      style={{ border: 0 }}
                      allowFullScreen
                      allow="camera; microphone; fullscreen; display-capture; autoplay"
                      sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation allow-top-navigation allow-downloads"
                      title="面试会议"
                    />
                  </div>
                ) : (
                  <div className="no-meeting">
                    <VideoCameraOutlined style={{ fontSize: 48, color: '#ccc' }} />
                    <p>暂无会议链接</p>
                  </div>
                )}

              </div>
            </div>

            <div className="right-section">
              <div className="section-title">转录信息</div>
              <div className="message-list">
                {/* '{messages.map((m) => (
              <div key={m.id} className="message-item">
                <div className="message-meta">
                  <span className={`avatar ${m.color}`}>{m.name[0]}</span>
                  <span className="name">{m.name}</span>
                  <span className="time">{m.time}</span>
                </div>
                <div className="message-bubble">
                  {m.text}
                </div>
              </div>
            ))}' */}
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                  rehypePlugins={[rehypeRaw]}
                  className="markdown-content"
                >
                  {finalTranscript}
                </ReactMarkdown>
              </div>
            </div>
          </div>
       ): (
        <Card size="small" className="info-card">
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 获取录制文件 */}
            <div className="record-files-section">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => handleGetRecordFiles()}
                  loading={loadingRecordFiles}
                  disabled={!meetingData?.webinarId || isRetrying}
                >
                  {isRetrying
                    ? `自动重试中 (${retryCountdown}s)`
                    : '获取录制文件'}
                </Button>
                {meetingData?.webinarId && (
                  <Text type="secondary">
                    会议ID: {meetingData.webinarId}
                  </Text>
                )}
              </Space>
              <Text type="secondary" style={{ fontSize: 12 }}>
                提示：面试结束后约5-10分钟可获取到录制文件
              </Text>

              {/* 倒计时提示 */}
              {isRetrying && (
                <div
                  style={{
                    padding: 8,
                    background: '#fff7e6',
                    border: '1px solid #ffd591',
                    borderRadius: 4,
                    fontSize: 12,
                    marginTop: 8,
                  }}
                >
                  <Space>
                    <Text type="warning">⏰</Text>
                    <Text type="warning">
                      未找到录制文件，将在 <strong>{retryCountdown}</strong>{' '}
                      秒后自动重试...
                    </Text>
                  </Space>
                </div>
              )}

              {recordFiles.length > 0 && (
                <div className="record-files-list">
                  <Title level={5}>录制文件列表:</Title>
                  <List
                    size="small"
                    dataSource={recordFiles}
                    renderItem={(file: any) => (
                      <List.Item>
                        <div style={{ width: '100%' }}>
                          <Space
                            direction="vertical"
                            style={{ width: '100%' }}
                          >
                            <Space>
                              <PlayCircleOutlined />
                              <Text strong>
                                {file.fileName || file.title || '录制文件'}
                              </Text>
                            </Space>
                            <Space>
                              <Text type="secondary">
                                时长: {file.duration || '未知'}
                              </Text>
                              <Text type="secondary">
                                时间:{' '}
                                {file.startTime || file.createdTime || '未知'}
                              </Text>
                              <Text type="secondary">
                                分辨率: {file.resolution || '未知'}
                              </Text>
                              <Text type="secondary">
                                大小: {file.fileSize || '未知'}
                              </Text>
                            </Space>
                            <Button
                              size="small"
                              type="primary"
                              onClick={() => setVideoUrl(file.url)}
                            >
                              使用此文件
                            </Button>
                          </Space>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </div>

            <Divider />

            {/* 录像地址输入 */}
            <Form form={form} layout="vertical">
              <Form.Item label="录像地址">
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    style={{ width: 'calc(100% - 100px)' }}
                    placeholder="请输入录像地址或从上方录制文件列表选择"
                    value={videoUrl}
                    onChange={(e) => setVideoUrl(e.target.value)}
                  />
                  <Button
                    type="primary"
                    onClick={handleAutoStart}
                    // onClick={handleStartTranscript}
                    loading={isTranscribing}
                    disabled={!videoUrl.trim()}
                  >
                    开始转录
                  </Button>
                </Space.Compact>
              </Form.Item>
            </Form>

            {/* 转录进度 */}
            {isTranscribing && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>转录进度：</Text>
                <Progress
                  percent={Math.round(transcriptProgress)}
                  status={transcriptProgress === 100 ? 'success' : 'active'}
                  style={{ marginTop: 8 }}
                />
              </div>
            )}

            {/* 实时转录片段 */}
            {transcriptSegments.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>实时转录片段：</Text>
                <div className="transcript-segments" style={{ marginTop: 8 }}>
                  {transcriptSegments.map((segment, index) => {
                    const startTime =
                      Math.floor(segment.start / 60) +
                      ':' +
                      Math.floor(segment.start % 60)
                        .toString()
                        .padStart(2, '0')
                    const endTime =
                      Math.floor(segment.end / 60) +
                      ':' +
                      Math.floor(segment.end % 60)
                        .toString()
                        .padStart(2, '0')
                    const speakerName =
                      segment.speaker_name ||
                      segment.speaker_id ||
                      segment.speaker ||
                      '未知说话人'

                    return (
                      <div
                        key={index}
                        className="segment-item"
                        style={{
                          marginBottom: 8,
                          padding: 8,
                          background: '#f6f8fa',
                          borderRadius: 4,
                        }}
                      >
                        <span
                          className="speaker-name"
                          style={{ fontWeight: 'bold', marginRight: 8 }}
                        >
                          {speakerName}
                        </span>
                        <span
                          className="time-range"
                          style={{ color: '#666', marginRight: 8 }}
                        >
                          ({startTime} - {endTime}):
                        </span>
                        <span className="segment-text">{segment.text}</span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </Space>
        </Card>
       )}
        
        
        
      </div>
    )
  }
)

TranscriptModule.displayName = 'TranscriptModule'

export default TranscriptModule
