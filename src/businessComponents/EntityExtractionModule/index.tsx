// 实体提取
import {
  <PERSON><PERSON>,
  Col,
  Row,
  Tabs,
  Card,
  Flex,
  Modal,
  Form,
  Select,
  Radio,
  message,
  Typography,
  theme,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import type { TabsProps } from "antd";
import useSSEChat from "@/hooks/useSSEChat";
import StreamTypewriter from "@/component/StreamTypewriter";
import {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { getItemDetail } from "@/api/public";
import { jfang, yfang } from "@/utils/keyPoints";
import { getToken, getUserInfo } from "@/utils/auth";
import TextArea from "antd/es/input/TextArea";
import "./index.less";

interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  pageInfo: any; // 当前的步骤信息
  onIncrement: () => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  unitInputData: {
    messages: string;
    chunks: any[]; // 切分后的数据
    fileList: []; // 文件列表
    isQuentially?: boolean; // 是否提取实体
    onDataReady?: (data: any) => void;
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const { Text } = Typography;
const { useToken } = theme;
const EntityExtractionModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        messages: "",
        fileList: [],
        chunks: [],
      },
      agentId,
      setGlobalLoading,
      unitOutPutData,
      pageInfo,
      onIncrement,
      onCallParent, // 调用输入输出评审
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [currentModal, setCurrentModal] = useState("1");
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    const scrollRef = useRef<HTMLDivElement>(null); // 滚动条
    const [workflowType, setWorkflowType] = useState("法审"); // 选择模式
    const [libSelectValue, setLibSelectValue] = useState<string[]>([]); // 敏感规则库
    const [ruleDbType, setRuleDbType] = useState<any>(""); // 规则
    const [libSelectName, setLibSelectName] = useState<any>(""); // 规则对应的知识库名称
    const [options, setOptions] = useState<any[]>([]);
    const [messageStr, setMessageStr] = useState<any>(""); // 返回的数据
    const [insArr, setInsArr] = useState<any>([]); // 实体提取的内容，但未输出质检
    const [knowledgeDataData, setKnowledgeDataData] = useState<any[]>([]);
    const [selectedStance, setSelectedStance] = useState("甲方"); // 甲方还是乙方
    const [angleSelect, setAngleSelect] = useState("1"); // 选择视角
    const [keyPoints, setKeyPoints] = useState("1"); // 选择视角
    const ruleFormData = useRef<any>({}); // 所有form选中的数据
    const [currentExtractModal, setCurrentExtractModal] = useState("1"); // 提取
    const [contractType, setContractType] = useState<string>(""); // 合同类型
    const [parsedData, setParsedData] = useState<any>([]); // 提及的所有数据
    const isEntity = useRef(false); // 是否已经提取实体
    const [loading, setLoading] = useState(false); // 是否已经流式完毕

    // 回显赋值
    useEffect(() => {
      if (isModalOpen) {
        if (
          unitOutPutData?.ruleFormData &&
          Object.keys(unitOutPutData?.ruleFormData).length > 0
        ) {
          Promise.resolve().then(() => {
            form.setFieldsValue(unitOutPutData?.ruleFormData);
            setWorkflowType(unitOutPutData?.ruleFormData.type_schema || ""); // 选择模式
            setRuleDbType(unitOutPutData?.ruleFormData.ruleDbType || ""); // 规则
            setSelectedStance(unitOutPutData?.ruleFormData.stance || ""); // 甲方还是乙方
            setAngleSelect(unitOutPutData?.ruleFormData.angleSelect || ""); // 选择视角
          });
        } else {
          Promise.resolve().then(() => {
            setAngleSelect("1"); // 选择视角
            setWorkflowType("法审");
            form.setFieldsValue({
              stance: "甲方",
              angleSelect: "1",
              keyPoints: "",
            });
            setKeyPoints(form.getFieldValue("keyPoints")); // 选择视角
          });
        }
      }
    }, [isModalOpen]);

    useEffect(() => {
      if (!unitOutPutData?.inputReview) {
        runSequentially();
        return;
      } else {
        setParsedData(unitOutPutData?.parsedData);
      }
    }, []);

    useEffect(() => {
      getItemDetail({
        pageNum: 1,
        pageSize: 300000,
        entity: {
          title: "",
          baseId:
            selectedStance == "甲方"
              ? "1948322515614875650"
              : "1948568996355682305",
          timeRange: "",
        },
      }).then((res) => {
        if (res.code === 200) {
          setKnowledgeDataData(res.data.records);
        }
      });
    }, [selectedStance]);

    const tabItems: TabsProps["items"] = [
      {
        key: "1",
        label: "实体提取",
      },
    ];
    // 页面选择视角的数据
    const angleView = [
      {
        id: "1",
        icon: "/copilot/src/assets/images/splitPreview/fawu.png",
        iconActive: "/copilot/src/assets/images/splitPreview/fawuactive.png",
        name: "法务",
        desc: "关注合同条款合规性、风险控制及法律责任界定",
      },
      {
        id: "2",
        icon: "/copilot/src/assets/images/splitPreview/caiwu.png",
        iconActive: "/copilot/src/assets/images/splitPreview/caiwuactive.png",
        name: "财务",
        desc: "关注付款条件、发票要求及财务相关条款",
      },
      {
        id: "3",
        icon: "/copilot/src/assets/images/splitPreview/jishu.png",
        iconActive: "/copilot/src/assets/images/splitPreview/jishuactive.png",
        name: "技术",
        desc: "关注技术规范、知识产权及保密条款",
      },
      {
        id: "4",
        icon: "/copilot/src/assets/images/splitPreview/xiaoshou.png",
        iconActive:
          "/copilot/src/assets/images/splitPreview/xiaoshouactive.png",
        name: "销售",
        desc: "关注商务条款，交付条件及售后服务",
      },
      {
        id: "5",
        icon: "/copilot/src/assets/images/splitPreview/caigou.png",
        iconActive: "/copilot/src/assets/images/splitPreview/caigouactive.png",
        name: "采购",
        desc: "关注采购条款、供应商资质及质量保证",
      },
    ];
    const entityTips = `1、【当事人信息】： 合同各方名称/姓名、地址、联系方式、身份信息（如法人需统一社会信用代码、法定代表人；自然人需身份证号）。
    2、【合同标的】： 合同涉及的对象（如买卖的货物、租赁的房屋、提供的服务、转让的技术等）。
    3、【数量/范围】： 标的的具体数量或服务/权利的范围。
    4、【质量/标准】： 对标的物或服务质量的要求。
    5、【价款/报酬/对价】： 一方为获得标的或服务需支付的金钱或其他形式的对价。
    6、【履行期限、地点、方式】： 各方义务完成的时间、地点和具体方法。
    7、【违约责任】： 一方不履行或不当履行合同时应承担的责任（违约金、赔偿损失等）。
    8、【争议解决】： 发生纠纷时的处理途径（诉讼或仲裁，及管辖地）。
    9、【合同生效与终止】： 合同生效的条件和终止的情形。
    10、【不可抗力】： 定义及发生后的处理。
    11、【通知与送达】： 双方有效通信的方式和地址。`;

    const extract = async (text: string) => {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      return new Promise((resolve) => {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId || "",
            agentId: agentId || "",
            path: "/chat-messages",
            difyJson: {
              inputs: {
                type: "实体提取",
              },
              pageinfo: pageInfo,
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: `需要提取的实体:${entityTips},合同内容:${text}`,
            },
          },
          query: {},
          onFinished: (result: any) => {
            resolve(result); // 将结果传出去
          },
        });
      });
    };

    const handleOk = async () => {
      form.validateFields().then(() => {
        const values = form.getFieldsValue();
        let angleSelectValue = "";
        angleView.forEach((item) => {
          if (item.id == form.getFieldValue("angleSelect")) {
            angleSelectValue = item.name;
          }
        });

        if (
          values.ruleDbType === "自定义知识库" &&
          libSelectValue.length === 0
        ) {
          message.error("请选择知识库");
          return;
        }
        let params = {
          type_schema: workflowType, // 模式分类
          contractType: contractType, // 合同类型
          standpoint: `${values.stance}${angleSelectValue}`, // 立场视角
          ruleDbType: `${values.ruleDbType}`, // 规则库类型
          key_points: values.keyPoints, // 审查要点
          Scene_type: "合同法审", // 场景类型
          RAG_Name: "", // 知识库名称
          scence_description: "合同法审", // 场景描述
        };
        ruleFormData.current = {
          ...params,
          stance: values.stance,
          angleSelect: values.angleSelect,
          angleSelectValue: angleSelectValue,
          keyPoints: values.keyPoints,
          libSelectValue: libSelectValue,
          libSelectName: libSelectName,
        };
        setIsModalOpen(false);
        onIncrement?.();
      });
    };
    // 合同质检
    const getQuality = async (arr: any) => {
      setGlobalLoading?.(true);
      let arrayData: any = [];
      setMessageStr("");
      setLoading(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      const text = arr.join(",");
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId || "",
          agentId: agentId || "",
          path: "/chat-messages",
          query: "1",
          difyJson: {
            inputs: {
              type: "输出质检",
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: text,
          },
        },
        query: {},
        onMessage: (result: any) => {
          console.log(result, 444);
          const cleanRes = result
            .replace(/```json\s*|```$/g, "")
            .trim()
            .replace(/```/g, "")
            .trim();
          setMessageStr(cleanRes);
        },
        onFinished: (result: any) => {
          const matches = result.match(/```json([\s\S]*?)```/g);
          if (matches) {
            matches.forEach((block) => {
              const jsonStr = block.replace(/```json|```/g, "").trim();
              try {
                const parsed = JSON.parse(jsonStr);

                if (Array.isArray(parsed)) {
                  arrayData = parsed; // 捕获数组
                }
              } catch (e) {
                console.error("JSON 解析失败:", e, jsonStr);
              }
            });
          }
          console.log(arrayData, "arrayData");
          setParsedData(arrayData);
          isEntity.current = true;
          setLoading(false);
          // 看有无输出评审
          if (!unitOutPutData?.outputReview) {
            onCallParent("输出", JSON.stringify(arrayData));
          } else {
            setGlobalLoading?.(false);
          }
        },
      });
    };
    // 获取合同类型
    const getContractType = async () => {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId || "",
          agentId: agentId || "",
          path: "/chat-messages",
          difyJson: {
            inputs: {
              type: "合同类型",
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: unitInputData?.chunks[0]?.text || "",
          },
        },
        query: {},
        onMessage: (result: any) => {},
        onFinished: (result: any) => {
          const cleanStr = result
            .replace(/<think>[\s\S]*?<\/think>/g, "")
            .trim();
          setContractType(cleanStr);
        },
      });
    };

    // 拿到实体提取的数据
    const runSequentially = async () => {
      console.log(unitOutPutData, 12);
      setGlobalLoading?.(true);
      getContractType(); // 获取合同类型
      if (!unitInputData?.chunks?.length) return;

      const arr: any[] = [];

      for (const item of unitInputData.chunks) {
        // 等待 extract 完成并拿到结果
        const res = await extract(item.text);
        arr.push(res);
      }
      if (!unitOutPutData?.inputReview) {
        const input = arr.join(",");
        setInsArr(arr);
        onCallParent("输入", input);
        return;
      }
    };
    const handleCancel = () => {
      form.resetFields();
      setIsModalOpen(false);
    };
    const showModal = (type: string) => {
      setIsModalOpen(true);
      setWorkflowType(type);
    };

    // 规则库数据
    const handleSelectChange = (value: string[], options: any) => {
      setLibSelectValue(value);
      const labels = options.map((opt) => opt.label);
      const joinedLabels = labels.join(", ");
      setLibSelectName(joinedLabels);
    };
    const items: TabsProps["items"] = [
      {
        key: "1",
        label: "选择审查身份",
      },
      {
        key: "2",
        label: "选择模式",
      },
    ];
    // 导入要点
    const importPoint = () => {
      let str = "";

      if (angleSelect == "1") {
        // 法务
        if (selectedStance == "甲方") {
          str = jfang?.fawu;
        } else {
          str = yfang?.fawu;
        }
      } else if (angleSelect == "2") {
        // 财务
        if (selectedStance == "甲方") {
          str = jfang?.caiwu;
        } else {
          str = yfang?.caiwu;
        }
      } else if (angleSelect == "3") {
        // 技术
        if (selectedStance == "甲方") {
          str = jfang?.jishu;
        } else {
          str = yfang?.jishu;
        }
      } else if (angleSelect == "4") {
        // 销售
        if (selectedStance == "甲方") {
          str = jfang?.xiaoshou;
        } else {
          str = yfang?.xiaoshou;
        }
      } else if (angleSelect == "5") {
        // 采购
        if (selectedStance == "甲方") {
          str = jfang?.caigou;
        } else {
          str = yfang?.caigou;
        }
      }
      form.setFieldsValue({ keyPoints: str });
      setKeyPoints(str);
    };
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          ruleFormData: ruleFormData.current, // form选中的数据
          isEntity: isEntity.current, // 是否提取实体
          parsedData: parsedData, // 提及的所有数据
        };
      },
      showModal: showModal, // 暴露给父组件的方法
      getQualityData: () => {
        console.log("到第二步了");
        getQuality(insArr);
      },
    }));
    return (
      <div style={{ height: "100%" }} className="entity-extraction">
        <Row style={{ height: "100%" }}>
          <Col xs={24} md={12}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="entity-file-list"
              items={unitInputData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div
                    style={{
                      minHeight: "calc(100vh - 213px)",
                      overflowY: "auto",
                    }}
                  >
                    <embed
                      style={{
                        width: "100%",
                        minHeight: "calc(100vh - 213px)",
                      }}
                      type="application/pdf"
                      src={x.url + "#toolbar=0&navpanes=0&scrollbar=0"}
                    ></embed>
                  </div>
                ),
              }))}
            />
          </Col>
          <Col xs={24} md={12} className="entity-right">
            <Tabs
              defaultActiveKey="1"
              items={tabItems}
              className="entity-file-view"
              onChange={(e) => {
                setCurrentExtractModal(e);
              }}
              activeKey={currentExtractModal}
            />
            <Flex className="contract-type" align="center">
              <span>合同类型：</span>
              {contractType}
            </Flex>
            {currentExtractModal == "1" && (
              <Row
                style={{
                  width: "100%",
                  height: "calc(100vh - 213px)",
                  overflowY: "scroll",
                }}
                ref={scrollRef}
              >
                <Flex vertical style={{ flex: 1 }}>
                  <div style={{ marginTop: token.marginSM }}>
                    {loading ? (
                      <StreamTypewriter
                        text={messageStr}
                        onchange={() => scroll()}
                      />
                    ) : (
                      <>
                        {parsedData?.map((item, index) => {
                          const termTitle = Object.keys(item)[0];
                          const termDetails = item[termTitle];

                          let content;
                          if (typeof termDetails === "string") {
                            content = <Text>{termDetails}</Text>;
                          } else if (
                            typeof termDetails === "object" &&
                            termDetails !== null
                          ) {
                            content = Object.entries(termDetails).map(
                              ([key, value]) => (
                                <div key={key} style={{ marginBottom: "12px" }}>
                                  <Text strong>{key}：</Text>
                                  <Text>{String(value)}</Text>
                                </div>
                              )
                            );
                          } else {
                            content = (
                              <Text type="secondary">无法识别的数据格式</Text>
                            );
                          }

                          return (
                            <Card
                              key={index}
                              title={<Text strong>{termTitle}</Text>}
                              className="split-preview-card"
                              style={{
                                width: "100%",
                                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                                marginBottom: 16,
                              }}
                            >
                              {content}
                            </Card>
                          );
                        })}
                      </>
                    )}
                  </div>
                </Flex>
              </Row>
            )}
          </Col>
        </Row>
        <Modal
          title=""
          maskClosable={false}
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          width={880}
          className="rule-modal-style"
        >
          <Tabs
            defaultActiveKey="1"
            items={items}
            onChange={(e) => {
              setCurrentModal(e);
            }}
            activeKey={currentModal}
          />
          <Form
            layout="vertical"
            form={form}
            initialValues={{ layout: "vertical" }}
            style={{
              overflow: "auto",
              maxHeight: "calc(100vh - 300px)",
            }}
          >
            <Flex
              style={{ display: currentModal === "1" ? "flex" : "none" }}
              vertical
            >
              <Text
                style={{
                  fontSize: token.fontSize,
                  opacity: "0.6",
                  marginBottom: token.marginMD,
                }}
              >
                选择您在合同中的角色，系统将根据不同视角提供针对性的审查重点
              </Text>
              <Form.Item
                label="选择立场"
                name="stance"
                style={{ marginBottom: 24 }}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
              >
                <Flex vertical>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: token.marginMD,
                      marginTop: token.marginSM,
                    }}
                  >
                    {/* 我是甲方 */}
                    <div
                      onClick={() => {
                        form.setFieldsValue({ stance: "甲方" });
                        setSelectedStance("甲方");
                      }}
                      style={{
                        border:
                          selectedStance === "甲方"
                            ? "1px solid #1677ff"
                            : "1px solid #eee",
                        borderRadius: token.borderRadius,
                        padding: "20px 20px 13px 20px",
                        cursor: "pointer",
                        background:
                          selectedStance === "甲方" ? "#e6f4ff" : "#F8FAFC",
                        display: "flex",
                        justifyContent: "space-between",
                        gap: token.marginSM,
                      }}
                    >
                      <div>
                        <div
                          style={{
                            fontWeight: 600,
                            color:
                              selectedStance === "甲方"
                                ? `${token.colorPrimary}`
                                : "#333",
                          }}
                        >
                          我是甲方
                        </div>
                        <div
                          style={{
                            fontSize: token.fontSizeSM,
                            color:
                              selectedStance === "甲方"
                                ? `${token.colorPrimary}`
                                : "#333",
                            marginTop: token.marginSM,
                            lineHeight: "22px",
                          }}
                        >
                          重点关注权益保障、违约责任等条款
                        </div>
                      </div>
                      <span>
                        <img
                          src="/copilot/src/assets/images/splitPreview/jiafang.png"
                          style={{ width: "39px", height: "58px" }}
                        />
                      </span>
                    </div>
                    {/* 我是乙方 */}
                    <div
                      onClick={() => {
                        form.setFieldsValue({ stance: "乙方" });
                        setSelectedStance("乙方");
                      }}
                      style={{
                        border:
                          selectedStance === "乙方"
                            ? `1px solid ${token.colorSuccess}`
                            : "1px solid #eee",
                        borderRadius: token.borderRadius,
                        padding: "20px 20px 13px 20px",
                        cursor: "pointer",
                        background:
                          selectedStance === "乙方" ? "#F6FFED" : "#F8FAFC",
                        display: "flex",
                        justifyContent: "space-between",
                        gap: token.marginSM,
                      }}
                    >
                      <div>
                        <div
                          style={{
                            fontWeight: 600,
                            color:
                              selectedStance === "乙方"
                                ? `${token.colorSuccess}`
                                : "#333",
                          }}
                        >
                          我是乙方
                        </div>
                        <div
                          style={{
                            fontSize: token.fontSizeSM,
                            color:
                              selectedStance === "乙方"
                                ? `${token.colorSuccess}`
                                : "#333",
                            marginTop: token.marginSM,
                            lineHeight: "22px",
                          }}
                        >
                          重点关注履约要求、付款条件等条款
                        </div>
                      </div>
                      <span>
                        <img
                          src="/copilot/src/assets/images/splitPreview/yifang.png"
                          style={{ width: "52px", height: "52px" }}
                        />
                      </span>
                    </div>
                  </div>
                </Flex>
              </Form.Item>
              <Form.Item
                label="选择视角"
                name="angleSelect"
                style={{ marginBottom: 24 }}
              >
                <Flex
                  align="center"
                  style={{
                    gap: token.marginSM,
                    marginTop: 10,
                  }}
                >
                  {angleView.map((item) => (
                    <Flex
                      onClick={() => {
                        form.setFieldsValue({ angleSelect: item.id });
                        setAngleSelect(item.id);
                      }}
                      vertical
                      style={{
                        border:
                          angleSelect === item.id
                            ? "1px solid #1677ff"
                            : "1px solid #eee",
                        borderRadius: token.borderRadius,
                        padding: token.paddingMD,
                        cursor: "pointer",
                        background:
                          angleSelect === item.id ? "#e6f4ff" : "#fafbfc",
                        display: "flex",
                        gap: token.marginXS,
                        width: 148, // 固定宽度
                        height: 174, // 固定高度
                      }}
                    >
                      <span style={{ fontSize: 28, color: "#1677ff" }}>
                        <img
                          src={
                            angleSelect == item.id ? item.iconActive : item.icon
                          }
                          style={{ width: 26, height: 26 }}
                        />
                      </span>
                      <div>
                        <Flex
                          style={{
                            fontWeight: 500,
                            fontSize: token.fontSizeLG,
                            color:
                              angleSelect === item.id
                                ? `${token.colorPrimary}`
                                : "#333",
                          }}
                        >
                          {item.name}
                        </Flex>
                        <div
                          style={{
                            fontSize: token.fontSizeSM,
                            lineHeight: "22px",
                            color:
                              angleSelect === item.id
                                ? `${token.colorPrimary}`
                                : "#333",
                          }}
                        >
                          {item.desc}
                        </div>
                      </div>
                    </Flex>
                  ))}
                </Flex>
              </Form.Item>
              <Form.Item
                label="审查要点"
                name="keyPoints"
                rules={[{ required: true, message: "请输入审查要点" }]}
                style={{ marginBottom: 24, position: "relative" }}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
              >
                <Button
                  type="link"
                  icon={<UploadOutlined />}
                  style={{
                    position: "absolute",
                    top: "-40px", // 对齐标签顶部
                    right: 0, // 贴右
                    zIndex: 1, // 确保按钮在输入框上方
                  }}
                  onClick={importPoint}
                >
                  导入通用要点
                </Button>
                <TextArea
                  showCount
                  onChange={(e) => {
                    form.setFieldsValue({ keyPoints: e.target.value });
                    setKeyPoints(e.target.value);
                  }}
                  value={keyPoints}
                  maxLength={20000}
                  placeholder="请输入审查要点"
                  style={{ height: 120, resize: "none" }}
                />
              </Form.Item>
            </Flex>
            <Flex
              style={{ display: currentModal === "2" ? "flex" : "none" }}
              vertical
            >
              {/* 四宫格模式选择区 */}
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 16,
                  marginBottom: 24,
                }}
              >
                {/* 合同法审 */}
                <div
                  onClick={() => setWorkflowType("法审")}
                  style={{
                    border:
                      workflowType === "法审"
                        ? `1px solid ${token.colorPrimary}`
                        : "1px solid #eee",
                    borderRadius: token.borderRadius,
                    padding: token.marginMD,
                    cursor: "pointer",
                    background:
                      workflowType === "法审"
                        ? `${token.colorPrimaryBg}`
                        : "#F8FAFC",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    gap: token.marginMD,
                  }}
                >
                  <div>
                    <div
                      style={{
                        fontWeight: 500,
                        fontSize: token.fontSizeLG,
                        color:
                          workflowType === "法审"
                            ? `${token.colorPrimary}`
                            : "#333",
                      }}
                    >
                      合同法审
                    </div>
                    <div
                      style={{
                        fontSize: token.fontSizeSM,
                        lineHeight: "22px",
                        color:
                          workflowType === "法审"
                            ? `${token.colorPrimary}`
                            : "#333",
                        marginTop: token.marginXS,
                      }}
                    >
                      智能合同审查与风险识别
                    </div>
                  </div>
                  <span>
                    <img
                      src="/copilot/src/assets/images/splitPreview/fashen.png"
                      style={{ width: 52, height: 52 }}
                    />
                  </span>
                </div>
                {/* 敏感词检出 */}
                <div
                  onClick={() => setWorkflowType("敏感词")}
                  style={{
                    border:
                      workflowType === "敏感词"
                        ? `1px solid ${token.colorWarning}`
                        : "1px solid #eee",
                    borderRadius: token.borderRadius,
                    padding: token.margin,
                    cursor: "pointer",
                    background:
                      workflowType === "敏感词"
                        ? `${token.colorWarningBg}`
                        : "#F8FAFC",
                    display: "flex",
                    justifyContent: "space-between",
                    gap: token.marginSM,
                    alignItems: "center",
                  }}
                >
                  <div>
                    <div
                      style={{
                        fontWeight: 500,
                        fontSize: token.fontSizeLG,
                        color:
                          workflowType === "敏感词"
                            ? `${token.colorWarning}`
                            : "#333",
                      }}
                    >
                      敏感词校验
                    </div>
                    <div
                      style={{
                        fontSize: token.fontSizeSM,
                        lineHeight: "22px",
                        color:
                          workflowType === "敏感词"
                            ? `${token.colorWarning}`
                            : "#333",
                        marginTop: token.marginXS,
                      }}
                    >
                      文本敏感内容智能识别
                    </div>
                  </div>
                  <span>
                    <img
                      src="/copilot/src/assets/images/splitPreview/mingan.png"
                      style={{ width: 52, height: 52 }}
                    />
                  </span>
                </div>
              </div>
              {/* 原有Form表单内容 */}
              {workflowType === "法审" ? (
                <>
                  {/* 四宫格模式选择区 */}
                  <Form.Item
                    label="规则类型(可多选)"
                    name="ruleDbType"
                    rules={[{ required: true, message: "请选择规则库类型" }]}
                  >
                    <Flex>
                      <Radio.Group
                        options={[
                          { value: "自定义知识库", label: "自定义知识库" },
                          { value: "民法典", label: "民法典" },
                          { value: "AI智能生成", label: "AI智能生成" },
                        ]}
                        style={{
                          color: "#333",
                        }}
                        onChange={(e) => {
                          console.log(e.target.value, 342);
                          form.setFieldsValue({ ruleDbType: e.target.value });
                          setRuleDbType(e.target.value);
                          setLibSelectName("");
                          setLibSelectValue([]);
                        }}
                      />
                    </Flex>
                  </Form.Item>
                  {ruleDbType == "自定义知识库" && (
                    <div>
                      <Select
                        mode="multiple"
                        style={{ width: "100%", marginTop: -20 }}
                        placeholder="请输入关键词筛选内部规则"
                        value={libSelectValue}
                        onChange={handleSelectChange}
                        showSearch
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={knowledgeDataData.map((item) => ({
                          value: item.id,
                          label: item.title,
                        }))}
                      />
                    </div>
                  )}
                </>
              ) : (
                <Form.Item label="敏感词规则库">
                  <Select
                    mode="multiple"
                    style={{ width: "100%", marginTop: -20 }}
                    placeholder="请选择内部规则"
                    value={libSelectValue}
                    onChange={handleSelectChange}
                    options={knowledgeDataData.map((item) => ({
                      value: item.id,
                      label: item.title,
                    }))}
                  />
                </Form.Item>
              )}
            </Flex>
          </Form>
        </Modal>
      </div>
    );
  }
);
// 添加 displayName
EntityExtractionModule.displayName = "EntityExtractionModule";
export default EntityExtractionModule;
