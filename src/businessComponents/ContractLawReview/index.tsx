import {
  Col,
  Row,
  Tabs,
  Card,
  Space,
  Tag,
  theme,
  Flex,
  Typography,
  Result,
} from "antd";
import {
  CheckCircleFilled,
  DownOutlined,
  ExclamationCircleFilled,
  InfoCircleFilled,
  UpOutlined,
  WarningFilled,
} from "@ant-design/icons";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { uploadChatFile } from "@/api/public";
import { getToken, getUserInfo } from "@/utils/auth";
import useSSEChat from "@/hooks/useSSEChat";
import { fileToBase64 } from "@/utils/common";
import StreamTypewriter from "@/component/StreamTypewriter";
import "./index.less";
import TabPane from "antd/es/tabs/TabPane";
interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  onGetReviewInfo: (type: string, data?: any) => void; // 调用场景输出评审
  onIncrement?: () => void; // 子组件调用下一步存储数据
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  pageInfo?: any; // 页面信息
  unitInputData: {
    targentData: []; // 传过来文件拆分块后选中的数据
    rulesData: []; // 所有的规则
    fileList: []; // 文件信息
    ruleFrom: any; // 之前选择的视角等数据
    originalFile: any; // 文件信息
    selectedRules: []; // 选择的规则
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}

const riskLevelColors: Record<string, string> = {
  高风险: "red",
  中风险: "orange",
  低风险: "blue",
};
const { Text } = Typography;
const { useToken } = theme;
const ContractLawReview = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        targentData: [],
        rulesData: [],
        ruleFrom: {},
        fileList: [],
        originalFile: null,
        selectedRules: [],
      },
      setGlobalLoading,
      agentId,
      pageInfo,
      onGetReviewInfo, // 调用场景输出评审
      onCallParent,
      onIncrement,
      unitOutPutData,
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [showType, setShowType] = useState("全部");
    const scrollRef = useRef<HTMLDivElement>(null);
    const [messageStr, setMessageStr] = useState<any>(""); // 返回的数据类型
    const [allData, setAllData] = useState<any>([]); // 所有的数据集合
    const items = [
      {
        key: "全部",
        label: <span> 全部（{allData.length}） </span>,
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "高风险",
        label: (
          <span>
            <ExclamationCircleFilled
              style={{ color: "#ff4d4f", marginRight: 8 }}
            />
            高风险（
            {allData.filter((x) => x.rule_level === "高风险").length}）
          </span>
        ),
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "中风险",
        label: (
          <span>
            <WarningFilled style={{ color: "#faad14", marginRight: 8 }} />
            中风险（
            {allData.filter((x) => x.rule_level === "中风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "低风险",
        label: (
          <span>
            <InfoCircleFilled style={{ color: "#fadb14", marginRight: 8 }} />
            低风险（
            {allData.filter((x) => x.rule_level === "低风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "已通过",
        label: (
          <span>
            <CheckCircleFilled style={{ color: "#52c41a", marginRight: 8 }} />
            已通过（
            {allData.filter((x) => x.rule_level === "已通过").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
    ];
    useEffect(() => {
      setGlobalLoading?.(false);
      if (!unitOutPutData?.inputReview) {
        getContractData();
        return;
      } else {
        console.log(unitOutPutData?.allData, 3434444);
        setAllData(unitOutPutData?.allData || []);
        setMessageStr(
          `<listing>${JSON.stringify(unitOutPutData?.allData)}</listing>`
        );
      }
    }, []);
    const getContractData = async () => {
      setMessageStr("");
      setGlobalLoading?.(true);

      // 拼接 str
      let str = "";
      unitInputData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`;
      });

      const results: any[] = []; // 用来收集每次 getLawData 的结果
      const data: any = []; // 选中的规则数据
      unitInputData?.rulesData?.forEach((item) => {
        if (unitInputData.selectedRules.includes(item.id)) {
          data.push(item);
        }
      });

      for (let i = 0; i < data?.length; i++) {
        if (i === 0 && !unitOutPutData?.inputReview) {
          onCallParent?.("输入", `${str},${data[0]?.title}`);
          return;
        } else {
          const res: any = await getLawData(data[i]?.title);
          const cleanRes = res
            .replace(/```json\s*|```$/g, "")
            .trim()
            .replace(/```/g, "")
            .trim();
          const jsonObject = JSON.parse(cleanRes)?.contract_review;
          results.push(jsonObject); // 👉 先收集起来
        }
      }

      // 👉 循环完统一更新
      if (results.length > 0) {
        const flatResults = results.flat();
        // setAllData((prev: any) => ({ ...prev, ...flatResults }));
        setAllData(flatResults);
        console.log(flatResults, 9999111);
        // 如果要拼接到 messageStr
        setMessageStr(`<listing>${JSON.stringify(flatResults)}</listing>`);
        if (!unitOutPutData?.outputReview) {
          onCallParent?.("输出", JSON.stringify(flatResults));
          onGetReviewInfo?.("输出", JSON.stringify(flatResults));
        } else {
          setGlobalLoading?.(false);
        }
        onIncrement?.(); // 调用下一步存储数据
      }
    };

    // 调取dify
    const getLawData = async (text: any) => {
      let str: any = "";
      unitInputData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`; // 用反引号拼接
      });
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      return new Promise((resolve) => {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId || "",
            agentId: agentId || "",
            path: "/chat-messages",
            difyJson: {
              inputs: {
                type: "规则审核",
              },
              pageinfo: pageInfo,
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: `${str},${text}`,
            },
          },
          query: {},
          onMessage: () => {},
          onFinished: (resultData: any) => {
            resolve(resultData); // 将结果传出去
          },
        });
      });
    };

    // 导出文件
    const exportContract = async () => {
      setGlobalLoading?.(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      console.log(unitInputData?.originalFile, 44444);
      const fileData = {
        fileName: unitInputData?.originalFile?.name,
        fileStr: await fileToBase64(unitInputData?.originalFile),
        path: "/files/upload",
        agentId: agentId,
        user: userInfo?.id,
        libName: unitInputData?.originalFile?.name,
        libDesc: "",
        flag: "file",
      };
      uploadChatFile(fileData).then(async (response: any) => {
        const fileData: any = [];
        fileData.push({
          type: "document",
          transfer_method: "local_file",
          upload_file_id: response?.data.id,
        });
        if (response.code == 200) {
          return new Promise((resolve) => {
            sseChat.start({
              url: "/dify/broker/agent/stream",
              headers: {
                "Content-Type": "application/json",
                Token: tokenInfo || "",
              },
              body: {
                insId: "1",
                bizType: "app:agent",
                bizId: agentId || "",
                agentId: agentId || "",
                path: "/chat-messages",
                difyJson: {
                  inputs: {
                    type: "文件下载",
                  },
                  pageinfo: pageInfo,
                  response_mode: "streaming",
                  user: userInfo?.id || "anonymous",
                  conversation_id: "",
                  query: `[{"rule_desc":"明确产品/服务单价、总价、含税价及适用税率；限定特批折扣有效期；赠品/附加服务需列明清单及价值。","rule_level":"高风险","risk_points":[{"original_text":"产品单价为100元，总价为1000元。","risk_warning":"未明确含税价及适用税率。","suggestion":"在条款中明确含税价及适用税率，例如：产品单价为100元（含税），总价为1000元（含税），适用税率为13%。"},{"original_text":"特批折扣为10%，有效期为一个月。","risk_warning":"特批折扣有效期描述不够具体。","suggestion":"明确特批折扣的具体有效期限，例如：特批折扣为10%，有效期为2023年10月1日至2023年10月31日。"},{"original_text":"赠品包括笔记本一本。","risk_warning":"未列明赠品价值。","suggestion":"在条款中列明赠品清单及价值，例如：赠品包括笔记本一本，价值50元。"}]},{"rule_desc":"付款节点绑定交付动作、明确逾期付款违约金、全款未付清前保留货物所有权/服务终止权","rule_level":"高风险","risk_points":[{"original_text":"付款节点绑定交付动作","risk_warning":"合同中未明确付款节点与交付动作的具体对应关系","suggestion":"建议在合同中明确列出每个付款节点对应的交付动作，确保双方责任清晰"},{"original_text":"明确逾期付款违约金","risk_warning":"合同中未具体规定逾期付款的违约金计算方式及金额","suggestion":"建议在合同中明确逾期付款违约金的计算公式和具体金额，以保障权益"},{"original_text":"全款未付清前保留货物所有权/服务终止权","risk_warning":"合同中未明确全款未付清时卖方保留货物所有权或服务终止权的具体操作流程","suggestion":"建议在合同中详细说明在全款未付清情况下，卖方如何行使保留货物所有权或终止服务的权利，避免争议"}]},{"rule_desc":"验收标准避免技术细节；争取客户签收即视为验收合格；允许分批交付缩短回款周期","rule_level":"高风险","risk_points":[{"original_text":"验收标准避免技术细节","risk_warning":"验收标准过于模糊，可能导致验收争议","suggestion":"明确验收标准，细化技术细节，确保双方对验收标准有清晰共识"},{"original_text":"争取客户签收即视为验收合格","risk_warning":"客户签收不等同于验收合格，可能导致质量问题和后续纠纷","suggestion":"明确验收流程，确保验收合格后再进行签收，避免仅凭签收视为验收合格"},{"original_text":"允许分批交付缩短回款周期","risk_warning":"分批交付可能导致项目整体进度和质量控制难度增加","suggestion":"明确分批交付的具体标准和验收流程，确保每批交付的质量和进度可控"}]},{"rule_desc":"客户需求变更需书面确认并触发调价权；设置宽限期条款","rule_level":"高风险","risk_points":[{"original_text":"客户需求变更需书面确认并触发调价权","risk_warning":"合同中未明确书面确认的具体形式和调价权的具体执行标准，可能导致执行过程中产生争议","suggestion":"建议在合同中明确书面确认的形式（如电子邮件、书面信函等）以及调价权的具体计算方法和执行流程"},{"original_text":"设置宽限期条款","risk_warning":"合同中未明确宽限期的时间长度和具体适用条件，可能导致违约责任界定不清","suggestion":"建议在合同中明确宽限期的具体天数及适用情形，并规定在宽限期内双方的权利义务"}]},{"rule_desc":"禁止客户采购竞品同类产品；约束客户不得反向仿制产品。","rule_level":"高风险","risk_points":[{"original_text":"禁止客户采购竞品同类产品","risk_warning":"该条款可能限制客户的选择权，违反公平竞争原则。","suggestion":"建议修改为：客户在采购同类产品时，应优先考虑我方产品，但不得强制限制客户选择其他竞品。"},{"original_text":"约束客户不得反向仿制产品","risk_warning":"该条款可能过于严格，限制客户的正常使用和技术研发。","suggestion":"建议修改为：客户不得未经授权反向仿制我方产品，但正常的维修、改进和使用除外。"}]},{"rule_desc":"优先续约权","rule_level":"高风险","risk_points":[{"original_text":"优先续约权","risk_warning":"条款未明确优先续约权的具体条件和执行方式，可能导致权利无法有效行使。","suggestion":"建议明确优先续约权的具体条件、行使期限和执行流程，确保权利可操作性。"}]},{"rule_desc":"老客户增购自动适用历史折扣","rule_level":"中风险","risk_points":[{"original_text":"老客户增购自动适用历史折扣","risk_warning":"条款未明确历史折扣的具体内容和适用范围，可能导致执行过程中产生争议。","suggestion":"建议明确历史折扣的具体内容、适用范围和计算方式，确保条款清晰无歧义。"}]},{"rule_desc":"约定转介绍奖励机制","rule_level":"低风险","risk_points":[{"original_text":"约定转介绍奖励机制","risk_warning":"条款未详细说明转介绍奖励的具体标准和发放条件，可能导致奖励发放不明确。","suggestion":"建议详细说明转介绍奖励的标准、发放条件和流程，确保奖励机制透明公正。"}]},{"rule_desc":"争议解决首选甲方所在地法院；避免仲裁；小额纠纷约定协商期≥15天再启动法律程序","rule_level":"高风险","risk_points":[{"original_text":"争议解决首选甲方所在地法院","risk_warning":"该条款可能导致乙方在争议解决中处于不利地位，增加诉讼成本","suggestion":"建议修改为双方协商确定争议解决地点，或选择中立第三方所在地法院"},{"original_text":"避免仲裁","risk_warning":"仲裁通常具有高效、保密等优点，完全避免仲裁可能不利于快速解决争议","suggestion":"建议保留仲裁选项，并明确仲裁机构和规则"},{"original_text":"小额纠纷约定协商期≥15天再启动法律程序","risk_warning":"协商期过长可能导致小额纠纷解决效率低下","suggestion":"建议缩短协商期至7-10天，以提高纠纷解决效率"}]},{"rule_desc":"明确客户承担费用（如差旅/安装费）","rule_level":"中风险","risk_points":[{"original_text":"客户需承担乙方工作人员的差旅费用及安装费用。","risk_warning":"条款中未明确具体费用标准和计算方式，可能导致后期费用争议。","suggestion":"建议增加具体费用标准和计算方式，例如：'客户需承担乙方工作人员的差旅费用及安装费用，具体费用标准按照附件一《费用明细表》执行。'"},{"original_text":"差旅及安装费用由客户另行支付。","risk_warning":"条款中未明确支付时间和支付方式，可能导致支付不及时。","suggestion":"建议明确支付时间和支付方式，例如：'差旅及安装费用由客户在收到乙方发票后10个工作日内支付至乙方指定账户。'"}]}]

`,
                  files: fileData,
                },
              },
              query: {},
              onMessage: () => {},
              onFinished: (res: any) => {
                const parenthesesContent = res.match(/\((.*?)\)/);
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null;

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/);
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null;

                if (parenthesesResult && squareBracketsResult) {
                  const link = document.createElement("a");
                  link.href = parenthesesResult;
                  link.download = `${squareBracketsResult}`;
                  document.body.appendChild(link);
                  link.click();
                  link.remove();
                }
                setGlobalLoading?.(false);
              },
            });
          });
        }
      });
    };
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          allData: allData,
        };
      },
      getContractData: () => {
        console.log("到第四步了");
        getContractData();
      },
      exportData: () => {
        console.log("到第四步了,导出了");
        exportContract();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="law-review">
        <Row style={{ height: "100%" }}>
          <Col xs={24} md={10}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="law-file-list"
              items={unitInputData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div style={{ minHeight: "calc(100vh - 160px)" }}>
                    <embed
                      style={{
                        width: "100%",
                        height: "100%",
                        minHeight: "calc(100vh - 160px)",
                      }}
                      type="application/pdf"
                      src={x.url + "#toolbar=0&navpanes=0&scrollbar=0"}
                    />
                  </div>
                ),
              }))}
            />
          </Col>

          <Col
            xs={24}
            md={14}
            className="law-right"
            style={{ height: "calc(100vh - 140px)" }}
          >
            <Flex className="law-right-header" gap={token.marginSM} vertical>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSizeXL,
                    fontWeight: "bold",
                    color: "#333",
                  }}
                  align="center"
                >
                  <Result status="success" />
                  审查结果
                </Flex>
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                  }}
                  gap={token.marginMD}
                >
                  <span>
                    当前立场：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.stance || ""}
                    </span>
                  </span>
                  <span>
                    审查视角：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.angleSelectValue || ""}
                    </span>
                  </span>
                  <span>
                    审核模式：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.type_schema || ""}
                    </span>
                  </span>
                </Flex>
              </Flex>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                    letterSpacing: "0.6px",
                    marginLeft: token.marginLG,
                  }}
                  gap={token.marginXS}
                >
                  共{allData.length}个风险点：高风险
                  {allData.filter((x) => x.rule_level === "高风险").length}
                  个，中风险
                  {allData.filter((x) => x.rule_level === "中风险").length}
                  个，低风险
                  {allData.filter((x) => x.rule_level === "低风险").length} 个
                </Flex>
              </Flex>
            </Flex>
            <Tabs
              defaultActiveKey="全部"
              activeKey={showType}
              onChange={(activeKey: string) => {
                setShowType(activeKey);
              }}
              items={items}
              className="law-file-view"
              tabBarStyle={{ marginBottom: 0 }}
              renderTabBar={(props, DefaultTabBar) => (
                <DefaultTabBar
                  {...props}
                  style={{ backgroundColor: "transparent" }}
                />
              )}
            />
            <div
              style={{ height: "calc(100vh - 320px)", overflow: "auto" }}
              ref={scrollRef}
            >
              <StreamTypewriter
                text={messageStr}
                onchange={() => {
                  scrollRef.current?.scrollTo({
                    top: scrollRef.current.scrollHeight,
                    behavior: "smooth",
                  });
                }}
                end={true}
                charsPerUpdate={5}
                components={{
                  listing({ children, className, ...props }: any) {
                    let isJSON = false;
                    let array: any = [];
                    console.log(children, 912);
                    try {
                      array = JSON.parse(children); // ✅ children 现在是合法 JSON
                      console.log(array, 888888888888);
                      isJSON = true;
                    } catch (error) {
                      console.log(3333321323);
                      isJSON = false;
                    }
                    const [expandedMap, setExpandedMap] = useState<
                      Record<number, boolean>
                    >({});

                    return isJSON ? (
                      <>
                        {array?.map((obj: any, index: number) => {
                          const expanded = expandedMap[index] ?? true; // 默认 true
                          return (
                            <>
                              {showType === obj?.rule_level ||
                              showType === "全部" ? (
                                <Card
                                  style={{ marginTop: "16px" }}
                                  className="law-contract-card"
                                  styles={{
                                    body: expanded
                                      ? { display: "none", padding: 0 }
                                      : {},
                                  }}
                                  title={
                                    <Flex
                                      justify="space-between"
                                      align="center"
                                    >
                                      <div
                                        style={{
                                          maxWidth: "calc(100% - 50px)",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          whiteSpace: "nowrap",
                                        }}
                                      >
                                        <Tag
                                          color={
                                            riskLevelColors[obj?.rule_level]
                                          }
                                        >
                                          {obj?.rule_level}
                                        </Tag>
                                        <Text>{obj?.rule_desc}</Text>
                                      </div>
                                      {expanded ? (
                                        <UpOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: false,
                                            }))
                                          }
                                        />
                                      ) : (
                                        <DownOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }))
                                          }
                                        />
                                      )}
                                    </Flex>
                                  }
                                >
                                  {!expanded && (
                                    <>
                                      {/* 风险点标签页 */}
                                      <Tabs defaultActiveKey="1">
                                        {obj?.risk_points?.map(
                                          (item: any, index: any) => (
                                            <TabPane
                                              tab={`风险点${index + 1}`}
                                              key={index + 1}
                                            >
                                              {/* 合同条款内容 */}
                                              <Text
                                                style={{
                                                  whiteSpace: "pre-line",
                                                }}
                                              >
                                                {item?.original_text || "--"}
                                              </Text>

                                              {/* 校验结果区域 */}
                                              <p
                                                style={{
                                                  color: "#333",
                                                  fontWeight: "bold",
                                                  margin: "16px 0px",
                                                  fontSize: "16px",
                                                  lineHeight: "22px",
                                                }}
                                              >
                                                校验结果
                                              </p>
                                              <Space
                                                direction="vertical"
                                                size={16}
                                                style={{ width: "100%" }}
                                              >
                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorErrorBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>风险提示：</Text>
                                                  <Text>
                                                    {item?.risk_warning || "--"}
                                                  </Text>
                                                </Card>

                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorPrimaryBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>修改意见：</Text>
                                                  <Text>
                                                    {item?.suggestion || "--"}
                                                  </Text>
                                                </Card>
                                              </Space>
                                            </TabPane>
                                          )
                                        )}
                                      </Tabs>
                                    </>
                                  )}
                                </Card>
                              ) : null}
                            </>
                          );
                        })}
                      </>
                    ) : (
                      <code
                        {...props}
                        className={className}
                        style={{
                          wordWrap: "break-word",
                          wordBreak: "break-all",
                          overflowWrap: "break-word",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        {children}
                      </code>
                    );
                  },
                }}
              />
            </div>
          </Col>
        </Row>
      </div>
    );
  }
);
// 添加 displayName
ContractLawReview.displayName = "SplitPreviewModule";
export default ContractLawReview;
