import { cacheClear } from "./cacheUtil";
import md5 from "crypto-js/md5";
import { routes } from "@/router";
import { processLatexCommands } from "./latex";
import { useNavigate } from "react-router-dom";
/**
 * 提取指定标签内的内容 第一个
 * @param str 字符串
 * @param tag 标签`
 * @returns 标签内的内容
 */
export const extractContent = (str: string, tag: string): string => {
  // 创建正则表达式以匹配指定标签内的内容
  const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, "gs");
  const matches: string[] = [];
  let match: RegExpExecArray | null;

  // 使用正则表达式提取内容
  while ((match = regex.exec(str)) !== null) {
    matches.push(match[1].trim()); // match[1] 是标签内的内容
  }

  return matches[0] || "";
};

/**
 * 检测数据是否空
 *
 * @param date 数据
 * @returns {boolean} 是否为空
 */
export function isNull(date: string | object | unknown): boolean {
  return date === undefined || date === null || date === "";
}

/**
 * 检测数据是否不为空
 *
 * @param date 数据
 * @returns {boolean} 是否不为空
 */
export function isNotNull(date: string | object | unknown): boolean {
  return !isNull(date);
}

// 创建一个导航方法
export function redirectToLogin() {
  cacheClear("token");
  cacheClear("tenantId");
  cacheClear("userInfo");
  // 获取浏览器地址栏的参数
  const urlParams = new URLSearchParams(window.location.search);
  // 获取参数值
  const tenantId = urlParams.get("tenantId") || "";
  const token = urlParams.get("token") || "";
  const fromtype = urlParams.get("fromtype") || "";
  if (fromtype === "app") {
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(
        JSON.stringify({
          type: "loginFailure",
          data: {
            token: token,
            tenantId: tenantId,
          },
        })
      );
    } else {
      console.warn("ReactNativeWebView is not defined");
    }
  } else {
    window.location.href = "/copilot/login";
  }
}

export function encryptToMD5(password: string) {
  return md5(password + "loHjqV9V3B1L8IRztEvL").toString();
}

/**
 * 提取指定标签的所有内容并放入数组（支持动态传入标签类型）
 * @param str 字符串
 * @param tag 要提取的标签名（如 "div"、"p"、"span"）
 * @returns 包含该标签所有内容的数组
 */
export const extractAllTagContents = (str: string, tag: string): string[] => {
  // 匹配指定标签的内容（支持自闭合标签）
  const regex = new RegExp(
    `<${tag}(?:\\s+[^>]*)?>(.*?)<\\/${tag}>|<${tag}(?:\\s+[^>]*)?\\/>`,
    "gs"
  );
  const matches: string[] = [];
  let match: RegExpExecArray | null;

  while ((match = regex.exec(str)) !== null) {
    // 如果是成对标签（如 <div>...</div>），提取内容
    if (match[1] !== undefined) {
      matches.push(match[1].trim());
    }
    // 如果是自闭合标签（如 <img />），可以返回空字符串或特殊标记
    else {
      matches.push(""); // 或者 `<${tag}/>`，取决于需求
    }
  }

  return matches;
};

// console.log(getNodeLevel("一、管理人基本情况"));  // 1
// console.log(getNodeLevel("（一）管理人概况"));    // 2
// console.log(getNodeLevel("1、管理人基本信息"));   // 3
// console.log(getNodeLevel("（1）资质信息"));       // 4
// console.log(getNodeLevel("a、补充说明"));        // 5
// console.log(getNodeLevel("其他内容"));           // 6
export const getNodeLevel = (title: string) => {
  // 匹配模式优先级从高到低
  const patterns = [
    { regex: /^([一二三四五六七八九十]+)、/, level: 1 }, // 第一层：一、二、
    { regex: /^（([一二三四五六七八九十]+)）/, level: 2 }, // 第二层：（一）（二）
    { regex: /^(\d+)、/, level: 3 }, // 第三层：1、2、
    { regex: /^（(\d+)）/, level: 4 }, // 第四层：（1）（2）
    { regex: /^([a-z])、/, level: 5 }, // 第五层：a、b、
  ];

  // 去除首尾空格后匹配
  const cleanTitle = title.trim();

  for (const { regex, level } of patterns) {
    if (regex.test(cleanTitle)) {
      return level;
    }
  }

  // 默认返回末级
  return 6;
};

export function mergeObjectsByKeyPro(arr: object[]) {
  const resultMap = new Map();
  const order: string[] = [];

  arr.forEach((obj) => {
    Object.entries(obj).forEach(([key, value]) => {
      if (!resultMap.has(key)) {
        resultMap.set(key, []);
        order.push(key);
      }
      resultMap.get(key).push(value);
    });
  });

  return order.map((key) => ({
    [key]: resultMap.get(key).join("\n"),
  }));
}

/**
 * 合并对象数组
 * @param arr|object 对象 | 对象数组
 * @returns 合并后每个对象只包含一个键值对
 * @example
 * 测试用例
 * const input1 = [{ a: 1 }, { b: 2 }];
 * const input2 = { a: 1, b: 2 };
 *console.log(getAllKeyValuePairs(input1)); // 输出: [['a', 1], ['b', 2]]
 *console.log(getAllKeyValuePairs(input2)); // 输出: [['a', 1], ['b', 2]]
 */
export function getAllKeyValuePairs(data: object | object[]) {
  let result = [];

  // 判断是否是数组
  if (Array.isArray(data)) {
    // 如果是数组，遍历每个对象
    data.forEach((item) => {
      if (typeof item === "object" && item !== null) {
        result.push(...Object.entries(item));
      }
    });
  } else if (typeof data === "object" && data !== null) {
    // 如果是普通对象，直接获取键值对
    result.push(...Object.entries(data));
  }

  return result;
}

export function updateSqlLimit(
  originalSql: string,
  page: number,
  pageSize: number
) {
  // 计算偏移量
  const offset = (page - 1) * pageSize;
  // 创建正则表达式，匹配 LIMIT 后的数字
  const limitRegex = /LIMIT\s*[^\n;]*/i;
  // 如果查询中存在 LIMIT，替换其为新的 LIMIT 和 OFFSET
  const updatedSql = originalSql.replace(
    limitRegex,
    `LIMIT ${pageSize} OFFSET ${offset}`
  );
  // 返回更新后的 SQL 查询
  return updatedSql;
}

export const getMdContent = (content: string) => {
  const regex = /```markdown([^```]*)```/;
  const match = content.match(regex);
  if (match) return match[1].trim();
  return content.replace("```markdown", "").replace("```", "");
};

export const getJSONContent = (content: string) => {
  const regex = /```json([^```]*)```/;
  const match = content.match(regex);
  if (match) return match[1].trim();
  return content.replace("```json", "").replace("```", "");
};

export function generateShortId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${random}`;
}

// 提取第一个完整JSON
export function extractFirstCompleteJSON(str: string): [string, string] | null {
  let stack = 0;
  let startIndex = -1;

  for (let i = 0; i < str.length; i++) {
    if (str[i] === "{") {
      if (stack === 0) startIndex = i;
      stack++;
    } else if (str[i] === "}") {
      stack--;
      if (stack === 0 && startIndex !== -1) {
        const jsonStr = str.slice(startIndex, i + 1);
        const fullMatch = str.slice(0, i + 1);
        return [fullMatch, jsonStr];
      }
    }
  }
  return null;
}

export function extractMarkdownFromString(text: string) {
  try {
    // 首先尝试匹配 ```markdown``` 格式
    const jsonBlockPattern = /```(?:markdown|MARKDOWN)\s*\n?(.*?)\s*```/s;
    let match = text.trim().match(jsonBlockPattern);

    if (match) {
      return match[1].trim();
    } else {
      const pattern = /{.*}/s;
      match = text.match(pattern);
      if (match) {
        return match[0].trim();
      }
    }
    return null;
  } catch (e) {
    throw new Error(`发生错误：${e}`);
  }
}

// 转base64
export function base64ToFile(base64: string, fileName: string) {
  // 检查 Base64 字符串格式
  const [prefix, base64Data] = base64.split(",");
  if (!base64Data) {
    throw new Error("Invalid Base64 string");
  }
  // 提取 MIME 类型
  const mimeType = prefix.match(/data:(.*?);base64/)?.[1] || "";
  // 解码 Base64 为二进制字符串
  const binaryString = atob(base64Data);
  // 创建二进制数组
  const binaryArray = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    binaryArray[i] = binaryString.charCodeAt(i);
  }
  // 创建 File 对象
  return new File([binaryArray], fileName, { type: mimeType });
}

// 文件转base64
export function fileToBase64(file: any) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    // 成功读取文件时的回调
    reader.onload = () => {
      resolve(reader.result); // Base64 编码的字符串
    };

    // 读取文件失败时的回调
    reader.onerror = (error) => {
      reject(error);
    };

    // 读取文件并转为 Base64
    reader.readAsDataURL(file);
  });
}

// 合同比对标签预处理
export const handlePreprocessText = (text: string): string => {
  // 标准化处理
  const processed = processLatexCommands(text)
    .replace(/\r\n|\r/g, "\n")
    .replace(/\s*(\d+)\s*页/g, "$1页") // 去除数字和"页"之间的空格
    .replace(/第\s+/g, "第") // 去除"第"后面的空格
    .replace(/\s+共/g, "共") // 去除"共"前面的空格
    .replace(/[\uFF01-\uFF5E]/g, (ch) =>
      String.fromCharCode(ch.charCodeAt(0) - 0xfee0)
    ) // 全角转半角
    .replace(/[“”]/g, '"')
    .replace(/[‘’]/g, "'")
    .replace(/—/g, "--")
    .replace(/…/g, "...")
    .replace(
      /[\s\u00A0\u1680\u180E\u2000-\u200B\u202F\u205F\u3000\uFEFF]+/g,
      " "
    )
    .replace(/\s+/g, "") // 移除所有空格 // 替换所有空白字符
    .replace(/[\[\]]/g, "")
    .replace(/(?<!\d)[:'"\s.。、，；‘’“”](?!\d)|(?<!\d[,.])[,'"](?!\d)/g, "")
    .replace("￥", "¥")
    // 移除不影响比对的常见字词
    .replace(
      /(的|和|等|之|与|及|或|而|且|但|然而|因此|所以|因为|由于|例如|比如|即|也就是说|换言之|简言之)/,
      ""
    )
    .toLowerCase();

  // 同义词替换
  // const synonymMap: Record<string, string> = {
  //   电脑: "计算机",
  //   网络: "互联网",
  // }

  return processed;
};

// 从字符串中安全提取 JSON：
// 1) 去掉 <think>...</think>
// 2) 优先提取 ```json ``` 代码块内容
// 3) 回退：截取第一个 '{' 到最后一个 '}' 之间的内容
export const extractJsonString = (raw: string): string => {
  if (!raw) return "";
  const noThink = raw.replace(/<think>[\s\S]*?<\/think>/g, "").trim();
  const fenced = noThink.match(/```(?:json)?\s*([\s\S]*?)```/i);
  if (fenced && fenced[1]) {
    return fenced[1].trim();
  }
  const first = noThink.indexOf("{");
  const last = noThink.lastIndexOf("}");
  if (first !== -1 && last !== -1 && last > first) {
    return noThink.slice(first, last + 1).trim();
  }
  return noThink;
};

// 获取所有路由
export const getRouterPaths = () => {
  const result: string[] = [];
  function traverse(list, parentPath = "") {
    list.forEach((route) => {
      const fullPath = route.path
        ? parentPath + "/" + route.path.replace(/^\//, "")
        : parentPath;
      result.push(fullPath || "/");
      if (route.children) {
        traverse(route.children, fullPath);
      }
    });
  }
  traverse(routes);
  return result;
};
