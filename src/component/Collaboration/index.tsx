import React, { useState, useMemo } from "react";
import { Modal } from "antd";
import { MentionsInput, Mention } from "react-mentions";

const inputStyles = {
  control: {
    fontSize: 14,
    lineHeight: "22px",
    minHeight: 120,
    border: "1px solid #d9d9d9",
    borderRadius: 8,
    background: "#fff",
    padding: 12,
  },
  highlighter: {
    padding: 12,
    overflow: "hidden",
    whiteSpace: "pre-wrap" as const,
    wordWrap: "break-word" as const,
  },
  input: {
    border: 0,
    outline: 0,
    boxShadow: "none",
    fontSize: 14,
    lineHeight: "22px",
  },
  suggestions: {
    list: {
      backgroundColor: "#fff",
      border: "1px solid rgba(0,0,0,0.15)",
      borderRadius: 8,
      maxHeight: 240,
      overflowY: "auto" as const,
    },
    item: {
      padding: "8px 12px",
      borderBottom: "1px solid #f5f5f5",
      "&focused": { backgroundColor: "#E6F4FF" },
    },
  },
};

const mentionStyle = {
  backgroundColor: "#E6F4FF",
  color: "#1677ff",
  fontWeight: 600,
  borderRadius: 4,
  padding: "0 4px",
};

interface User {
  id: string;
  display: string;
}

const users: User[] = [
  { id: "1", display: "张三" },
  { id: "2", display: "李四" },
];

interface MentionModalProps {
  open: boolean;
  onOk: (value: string) => void;
  onCancel: () => void;
}

const MentionModal: React.FC<MentionModalProps> = ({
  open,
  onOk,
  onCancel,
}) => {
  const [value, setValue] = useState("");
  const [mention, setMention] = useState<User | null>(null);

  // 检查是否已有 mention
  const hasMention = useMemo(() => !!mention, [mention]);

  // 转纯文本返回
  const toPlainText = (v: string) =>
    v.replace(/@\[(.+?)\]\([^)]+\)/g, (_m, display) => `@${display}`);

  // onChange：同步 value，并检测 mention 是否被删除
  const handleChange = (e: any) => {
    const val = e.target.value;
    setValue(val);

    if (mention) {
      const markupRegex = new RegExp(
        `@\\[${mention.display}\\]\\(${mention.id}\\)`
      );
      if (!markupRegex.test(val)) {
        setMention(null); // mention 被删除，允许再次 @
      }
    }
  };

  // onAdd：记录 mention
  const handleAdd = (id: string, display: string) => {
    setMention({ id, display });
  };

  return (
    <Modal
      title="描述"
      open={open}
      onOk={() => onOk(toPlainText(value))}
      onCancel={onCancel}
      destroyOnClose
    >
      <MentionsInput
        value={value}
        onChange={handleChange}
        style={inputStyles}
        placeholder="请输入内容，使用 @ 选择人员（仅限一人）"
        allowSuggestionsAboveCursor
      >
        <Mention
          trigger="@"
          data={hasMention ? [] : users} // 已有一个 mention 禁止再次选择
          style={mentionStyle}
          markup="@\[__display__\](__id__)"
          displayTransform={(_id, display) => `@${display}`}
          onAdd={handleAdd}
        />
      </MentionsInput>
    </Modal>
  );
};

export default MentionModal;
